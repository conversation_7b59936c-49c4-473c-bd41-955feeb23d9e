package com.zenyte.game.world.region.area.wilderness;

import com.zenyte.game.content.skills.agility.WildernessAgilityLapTracker;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.region.RSPolygon;
import com.zenyte.game.world.region.area.plugins.LoginPlugin;

public class WildernessAgilityCourseArea extends WildernessArea implements LoginPlugin {
    @Override
    public RSPolygon[] polygons() {
        return new RSPolygon[] {
                new RSPolygon(new int[][]{
                        { 2991, 3931 },
                        { 2988, 3934 },
                        { 2988, 3945 },
                        { 2991, 3946 },
                        { 2993, 3948 },
                        { 2993, 3949 },
                        { 2990 ,3953 },
                        { 2990, 3963 },
                        { 2995, 3966 },
                        { 3005, 3966 },
                        { 3007, 3964 },
                        { 3007, 3961 },
                        { 3009, 3959 },
                        { 3009, 3953 },
                        { 3008, 3951 },
                        { 3008, 3947 },
                        { 3006, 3946 },
                        { 3006, 3933 },
                        { 3003, 3931 },
                })
        };
    }

    @Override
    public void enter(final Player player) {
        WildernessAgilityLapTracker.resetLapCount(player);
    }
    @Override
    public void login(final Player player) {
        player.sendMessage("Your wilderness agility lap streak has been reset.");
    }
    @Override
    public void leave(final Player player, final boolean logout) {
        WildernessAgilityLapTracker.resetLapCount(player);
    }

    @Override
    public String name() {
        return "Wilderness Agility Course";
    }
}
