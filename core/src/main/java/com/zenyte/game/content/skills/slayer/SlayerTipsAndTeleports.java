package com.zenyte.game.content.skills.slayer;

import com.zenyte.game.item.Item;
import com.zenyte.game.content.skills.magic.spells.teleports.Teleport;
import com.zenyte.game.content.skills.magic.spells.teleports.TeleportType;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.npc.NPC;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.plugins.dialogue.OptionsMenuD;
import com.zenyte.game.world.entity.player.privilege.MemberRank;

import java.util.ArrayList;
import java.util.List;

/**
 * Handles slayer tips and teleports for players using a unified interface.
 * All players see the same interface with task tips and red location names.
 * Donators can click location names to teleport, non-donators get a message about needing donator status.
 *
 * <AUTHOR> Assistant
 */
public class SlayerTipsAndTeleports {

    /**
     * Shows the unified slayer tips and teleports interface.
     * All players see the same interface with task tips and red location names.
     * Donators can click location names to teleport, non-donators get a donator message.
     *
     * @param player The player requesting the interface
     * @param npc The NPC to use for dialogue (can be null for default)
     */
    public static void showSlayerTips(Player player, NPC npc) {
        showSlayerTipsAndTeleports(player, npc);
    }

    /**
     * Shows the unified slayer tips and teleports interface.
     * All players see the same OptionsMenuD interface with task tips and red location names.
     * Donators can click location names to teleport.
     * Non-donators get a message about needing donator status when clicking locations.
     *
     * @param player The player requesting the interface
     * @param npc The NPC to use for dialogue (can be null for default)
     */
    public static void showSlayerTipsAndTeleports(Player player, NPC npc) {
        Assignment assignment = player.getSlayer().getAssignment();
        if (assignment == null) {
            player.sendMessage("You don't have a slayer task assigned.");
            return;
        }

        SlayerTask task = assignment.getTask();
        List<LocationInfo> locations = getTaskLocations(task);

        if (locations.isEmpty()) {
            player.sendMessage("No location information is available for your current task: " + task.toString());
            return;
        }

        // Show all locations interface
        showAllLocationsInterface(player, npc, task, locations);
    }

    /**
     * Shows the unified OptionsMenuD interface for all players.
     * Interface shows task tip first (non-clickable), then red location names.
     * All players see the same visual interface.
     * Donators can click location names to teleport.
     * Non-donators get a donator message when clicking location names.
     *
     * @param player The player
     * @param npc The NPC to use for dialogue (can be null for default)
     * @param task The slayer task
     * @param locations List of all locations
     */
    private static void showAllLocationsInterface(Player player, NPC npc, SlayerTask task, List<LocationInfo> locations) {
        boolean isDonator = player.getMemberRank().equalToOrGreaterThan(MemberRank.REGULAR);

        // Create unified interface: task tip (non-clickable), then red location names for all players
        String[] menuOptions = new String[locations.size() + 1];
        menuOptions[0] = task.getTip(); // Task tip (non-clickable)

        // All players see red location names - only click behavior differs
        for (int i = 0; i < locations.size(); i++) {
            LocationInfo location = locations.get(i);
            menuOptions[i + 1] = location.getLocationName();
        }

        player.getDialogueManager().start(new OptionsMenuD(player,
            "Slayer Tips & Teleports - " + task.toString(), menuOptions) {
            @Override
            public void handleClick(int slotId) {
                if (slotId > 0 && slotId < menuOptions.length) {
                    // Location name clicked
                    int locationIndex = slotId - 1;
                    if (locationIndex < locations.size()) {
                        LocationInfo location = locations.get(locationIndex);
                        if (isDonator) {
                            handleTeleport(player, task, location);
                        } else {
                            player.sendMessage("You need to be a donator to teleport to slayer locations.");
                            showAllLocationsInterface(player, npc, task, locations);
                        }
                    }
                } else {
                    // Task tip clicked - reopen interface
                    showAllLocationsInterface(player, npc, task, locations);
                }
            }
        });
    }

    /**
     * Handles teleportation to a specific location.
     *
     * @param player The player
     * @param task The slayer task
     * @param location The location to teleport to
     */
    private static void handleTeleport(Player player, SlayerTask task, LocationInfo location) {
        Location teleportLoc = location.getTeleportLocation();

        // Safety check for placeholder/invalid locations
        if (teleportLoc.getX() == -1 && teleportLoc.getY() == -1) {
            player.sendMessage("Teleport is not setup for this slayer monster.");
            player.sendMessage("Location: " + location.getLocationName() + " (" + task.toString() + ")");
            return;
        }

        new SlayerTaskTeleport(teleportLoc).teleport(player);
        player.sendMessage("Teleported to " + location.getLocationName() + " for your slayer task.");
    }

    /**
     * Gets all location information for a given slayer task.
     *
     * @param task The slayer task
     * @return List of location information
     */
    private static List<LocationInfo> getTaskLocations(SlayerTask task) {
        List<LocationInfo> locations = new ArrayList<>();

        if (task instanceof RegularTask) {
            RegularTask regularTask = (RegularTask) task;
            List<RegularTask.SlayerLocationInfo> taskLocations = regularTask.getLocations();
            for (RegularTask.SlayerLocationInfo loc : taskLocations) {
                locations.add(new LocationInfo(loc.getLocationName(), loc.getTeleportLocation()));
            }
        } else if (task instanceof BossTaskSumona) {
            BossTaskSumona bossTask = (BossTaskSumona) task;
            List<BossTaskSumona.SlayerLocationInfo> taskLocations = bossTask.getLocations();
            for (BossTaskSumona.SlayerLocationInfo loc : taskLocations) {
                locations.add(new LocationInfo(loc.getLocationName(), loc.getTeleportLocation()));
            }
        }

        return locations;
    }



    /**
     * Teleport class for slayer task locations.
     */
    public static class SlayerTaskTeleport implements Teleport {
        private final Location destination;

        public SlayerTaskTeleport(Location destination) {
            this.destination = destination;
        }

        @Override
        public TeleportType getType() {
            return TeleportType.REGULAR_TELEPORT;
        }

        @Override
        public Location getDestination() {
            return destination;
        }

        @Override
        public int getLevel() {
            return 0;
        }

        @Override
        public double getExperience() {
            return 0;
        }

        @Override
        public int getRandomizationDistance() {
            return 0;
        }

        @Override
        public Item[] getRunes() {
            return null;
        }

        @Override
        public int getWildernessLevel() {
            return WILDERNESS_LEVEL;
        }

        @Override
        public boolean isCombatRestricted() {
            return UNRESTRICTED;
        }
    }

    /**
     * Unified location information class.
     */
    private static class LocationInfo {
        private final String locationName;
        private final Location teleportLocation;

        public LocationInfo(String locationName, Location teleportLocation) {
            this.locationName = locationName;
            this.teleportLocation = teleportLocation;
        }

        public String getLocationName() { return locationName; }
        public Location getTeleportLocation() { return teleportLocation; }
    }
}
