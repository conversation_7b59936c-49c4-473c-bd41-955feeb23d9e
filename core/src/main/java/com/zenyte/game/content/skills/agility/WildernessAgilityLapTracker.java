package com.zenyte.game.content.skills.agility;

import com.zenyte.game.world.entity.player.Player;

/**
 * <AUTHOR> Assistant
 * Utility class for tracking wilderness agility lap counts
 * This class is in the core module so it can be accessed by Player.java
 */
public class WildernessAgilityLapTracker {

    private static final String LAP_COUNT_ATTRIBUTE = "wilderness_agility_laps";

    /**
     * Increments and returns the player's current lap count for this session
     * Uses temporary attributes so it resets on logout
     */
    public static int incrementLapCount(Player player) {
        // Use temporary attributes - these are not saved to character file
        int currentLaps = player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
        int newLapCount = currentLaps + 1;
        player.addTemporaryAttribute(LAP_COUNT_ATTRIBUTE, newLapCount);
        
        // Send message to player about their streak
        if (newLapCount == 1) {
            player.sendMessage("You've completed your first lap! Lap streak: " + newLapCount);
        } else if (newLapCount % 10 == 0) {
            player.sendMessage("Lap streak milestone reached! Current streak: " + newLapCount);
        } else if (newLapCount == 16 || newLapCount == 31 || newLapCount == 61) {
            player.sendMessage("Lap streak: " + newLapCount + " - Your rewards have improved!");
        }
        
        return newLapCount;
    }

    /**
     * Gets the player's current lap count without incrementing
     */
    public static int getCurrentLapCount(Player player) {
        return player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
    }

    /**
     * Resets the player's lap count (called when leaving course area)
     */
    public static void resetLapCount(Player player) {
        int currentLaps = player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
        if (currentLaps > 0) {
            player.addTemporaryAttribute(LAP_COUNT_ATTRIBUTE, 0);
            player.sendMessage("Your wilderness agility lap streak has been reset.");
        }
    }

    /**
     * Reduces lap count by 10 on logout (as per OSRS mechanics)
     * This is called from Player.finish() method
     */
    public static void reduceLapCountOnLogout(Player player) {
        int currentLaps = player.getNumericTemporaryAttribute(LAP_COUNT_ATTRIBUTE).intValue();
        if (currentLaps > 0) {
            int newLapCount = Math.max(0, currentLaps - 10);
            player.addTemporaryAttribute(LAP_COUNT_ATTRIBUTE, newLapCount);
            if (newLapCount > 0) {
                player.sendMessage("Your wilderness agility lap streak has been reduced by 10 due to logout. Current streak: " + newLapCount);
            } else {
                player.sendMessage("Your wilderness agility lap streak has been reset due to logout.");
            }
        }
    }
}
