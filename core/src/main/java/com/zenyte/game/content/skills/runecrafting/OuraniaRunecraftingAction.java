package com.zenyte.game.content.skills.runecrafting;

import com.zenyte.game.content.achievementdiary.diaries.ArdougneDiary;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.masks.Graphics;
import com.zenyte.game.world.entity.player.Action;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.SkillConstants;

/**
 * <AUTHOR> Assistant
 * Handles the Ourania (ZMI) Altar runecrafting action
 */
public class OuraniaRunecraftingAction extends Action {
    
    private static final Animation RUNECRAFTING_ANIM = new Animation(791);
    private static final Graphics RUNECRAFTING_GFX = new Graphics(186, 0, 96);
    
    // Rune IDs based on the wiki and codebase analysis
    private static final int[] RUNE_IDS = {
        556, // Air rune
        558, // Mind rune  
        555, // Water rune
        557, // Earth rune
        554, // Fire rune
        559, // Body rune
        564, // Cosmic rune
        562, // Chaos rune
        9075, // Astral rune
        561, // Nature rune
        563, // Law rune
        560, // Death rune
        565, // Blood rune
        566  // Soul rune
    };
    
    // Base experience values for each rune type (from Runecrafting enum)
    private static final double[] RUNE_EXPERIENCE = {
        5.0,   // Air
        5.5,   // Mind
        6.0,   // Water
        6.5,   // Earth
        7.0,   // Fire
        7.5,   // Body
        8.0,   // Cosmic
        8.5,   // Chaos
        8.7,   // Astral
        9.0,   // Nature
        9.5,   // Law
        10.0,  // Death
        10.5,  // Blood
        29.7   // Soul
    };

    @Override
    public boolean start() {
        if (!player.getInventory().containsItem(7936, 1) &&
            !player.getInventory().containsItem(24704, 1)) {
            player.sendMessage("You need some pure essence or daeyalt essence to craft runes at this altar.");
            return false;
        }

        player.setAnimation(RUNECRAFTING_ANIM);
        player.setGraphics(RUNECRAFTING_GFX);
        return true;
    }

    @Override
    public boolean process() {
        return true;
    }

    @Override
    public int processWithDelay() {
        // Determine which essence to use
        boolean useDaeyalt = player.getInventory().containsItem(24704, 1);
        int essenceId = useDaeyalt ? 24704 : 7936;
        int essenceAmount = player.getInventory().getAmountOf(essenceId);
        
        if (essenceAmount == 0) {
            return -1;
        }
        
        // Remove all essence
        player.getInventory().deleteItem(essenceId, essenceAmount);
        
        // Calculate total experience and runes created
        double totalExperience = 0;
        int totalRunes = 0;
        
        // Process each essence individually
        for (int i = 0; i < essenceAmount; i++) {
            // Get random rune based on player's level
            OuraniaRune randomRune = getRandomRune(player.getSkills().getLevel(SkillConstants.RUNECRAFTING));
            
            // Calculate experience (1.7x multiplier, 2.55x for daeyalt)
            double experience = randomRune.getExperience() * 1.7;
            if (useDaeyalt) {
                experience *= 1.5; // Additional 1.5x for daeyalt (total 2.55x)
            }
            totalExperience += experience;
            
            // Create base rune
            int runeAmount = 1;
            
            // Check for Ardougne Medium Diary bonus
            if (player.getAchievementDiaries().isCompleted(ArdougneDiary.MEDIUM)) {
                double bonusChance = getArdougneBonusChance(randomRune.getRuneId());
                if (Utils.randomDouble() < bonusChance) {
                    runeAmount = 2; // Double the rune
                }
            }
            
            player.getInventory().addItem(new Item(randomRune.getRuneId(), runeAmount));
            totalRunes += runeAmount;
        }
        
        // Add experience
        player.getSkills().addXp(SkillConstants.RUNECRAFTING, totalExperience);
        
        // Send message
        String essenceType = useDaeyalt ? "daeyalt essence" : "pure essence";
        player.sendFilteredMessage("You bind the altar's power into " + totalRunes + " random runes using " + essenceAmount + " " + essenceType + ".");
        
        return -1;
    }
    
    /**
     * Gets a random rune based on the player's Runecrafting level using the distribution from the wiki
     */
    private OuraniaRune getRandomRune(int runecraftingLevel) {
        // Get the appropriate level band (1-9, 10-19, 20-29, etc.)
        int levelBand = Math.min(runecraftingLevel / 10, 9); // Cap at 90-99 band
        if (runecraftingLevel >= 99) {
            levelBand = 10; // Special 99+ band
        }
        
        // Distribution percentages from the wiki (multiplied by 100 for easier calculation)
        double[][] distributions = getRuneDistributions();
        double[] currentDistribution = distributions[levelBand];
        
        // Generate random number and find which rune it corresponds to
        double random = Utils.randomDouble() * 100.0;
        double cumulative = 0.0;
        
        for (int i = 0; i < currentDistribution.length; i++) {
            cumulative += currentDistribution[i];
            if (random <= cumulative) {
                return new OuraniaRune(RUNE_IDS[i], RUNE_EXPERIENCE[i]);
            }
        }
        
        // Fallback to air rune
        return new OuraniaRune(RUNE_IDS[0], RUNE_EXPERIENCE[0]);
    }
    
    /**
     * Gets the Ardougne Diary bonus chance for a specific rune
     */
    private double getArdougneBonusChance(int runeId) {
        // Chances from the wiki
        switch (runeId) {
            case 556: // Air
            case 558: // Mind
            case 555: // Water
            case 557: // Earth
            case 554: // Fire
            case 559: // Body
            case 564: // Cosmic
            case 562: // Chaos
            case 9075: // Astral
                return 0.25; // 25%
            case 561: // Nature
                return 0.225; // 22.5%
            case 563: // Law
                return 0.20; // 20%
            case 560: // Death
                return 0.175; // 17.5%
            case 565: // Blood
                return 0.15; // 15%
            case 566: // Soul
                return 0.10; // 10%
            default:
                return 0.0;
        }
    }
    
    /**
     * Returns the rune distribution table from the wiki
     * Each row represents a level band, each column represents a rune type
     */
    private double[][] getRuneDistributions() {
        return new double[][] {
            // Level 1-9: Soul, Blood, Death, Law, Nature, Astral, Chaos, Cosmic, Body, Fire, Earth, Water, Mind, Air
            {0.03, 0.05, 0.08, 0.15, 0.30, 0.45, 0.60, 0.85, 1.50, 3.00, 6.00, 12.00, 25.00, 50.00},
            // Level 10-19
            {0.04, 0.06, 0.12, 0.24, 0.40, 0.60, 0.80, 1.75, 6.00, 12.00, 24.00, 21.00, 18.00, 15.00},
            // Level 20-29
            {0.09, 0.15, 0.32, 0.55, 1.10, 2.10, 4.20, 8.00, 16.00, 15.00, 14.00, 13.50, 13.00, 12.00},
            // Level 30-39
            {0.21, 0.40, 0.60, 1.30, 2.50, 5.00, 10.00, 20.00, 13.00, 12.00, 11.00, 9.00, 8.00, 7.00},
            // Level 40-49
            {0.41, 0.80, 1.20, 2.60, 5.00, 10.00, 20.00, 15.00, 10.00, 8.00, 7.50, 7.00, 6.50, 6.00},
            // Level 50-59
            {0.81, 1.70, 3.50, 7.00, 13.50, 15.00, 11.00, 10.00, 7.50, 7.00, 6.50, 6.00, 5.50, 5.00},
            // Level 60-69
            {1.01, 2.00, 4.00, 8.00, 15.50, 14.00, 10.50, 9.50, 7.50, 7.00, 6.00, 5.50, 5.00, 4.50},
            // Level 70-79
            {2.01, 5.00, 10.00, 18.00, 15.00, 12.00, 9.00, 7.00, 5.00, 4.00, 4.00, 3.00, 3.00, 3.00},
            // Level 80-89
            {4.01, 6.00, 14.50, 14.50, 13.50, 10.50, 8.00, 7.00, 6.00, 9.00, 3.00, 2.00, 1.00, 1.00},
            // Level 90-98
            {6.51, 10.00, 16.50, 14.50, 13.50, 10.00, 7.00, 6.00, 5.00, 4.00, 3.00, 2.00, 1.00, 1.00},
            // Level 99+
            {9.01, 13.00, 15.50, 14.50, 13.50, 9.50, 6.00, 5.00, 4.00, 3.00, 3.00, 2.00, 1.00, 1.00}
        };
    }
    
    /**
     * Helper class to store rune information
     */
    private static class OuraniaRune {
        private final int runeId;
        private final double experience;
        
        public OuraniaRune(int runeId, double experience) {
            this.runeId = runeId;
            this.experience = experience;
        }
        
        public int getRuneId() {
            return runeId;
        }
        
        public double getExperience() {
            return experience;
        }
    }
}
