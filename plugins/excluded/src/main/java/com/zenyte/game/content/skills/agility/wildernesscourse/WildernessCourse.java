package com.zenyte.game.content.skills.agility.wildernesscourse;

import com.zenyte.game.content.skills.agility.AbstractAgilityCourse;
import com.zenyte.game.item.Item;
import com.zenyte.game.item.ItemId;
import com.zenyte.game.util.Utils;
import com.zenyte.game.world.entity.player.Player;

import java.util.function.Consumer;

/**
 * <AUTHOR> | 24 feb. 2018 : 23:24:01
 * @see <a href="https://www.rune-server.ee/members/tommeh/">Rune-Server profile</a>}
 */
public final class WildernessCourse extends AbstractAgilityCourse {

    /**
     * Enum representing extra supplies that can be dropped (if inventory space available)
     */
    public enum ExtraSupplies {
        BLIGHTED_ANGLERFISH(ItemId.BLIGHTED_ANGLERFISH, 5), // 5/16 chance
        BLIGHTED_MANTA_RAY(ItemId.BLIGHTED_MANTA_RAY, 5),   // 5/16 chance
        BLIGHTED_KARAMBWAN(ItemId.BLIGHTED_KARAMBWAN, 5),   // 5/16 chance
        BLIGHTED_SUPER_RESTORE(ItemId.BLIGHTED_SUPER_RESTORE4, 1); // 1/16 chance

        private final int itemId;
        private final int weight;

        public static final ExtraSupplies[] VALUES = values();
        private static final int TOTAL_WEIGHT = 16;

        ExtraSupplies(int itemId, int weight) {
            this.itemId = itemId;
            this.weight = weight;
        }

        public int getItemId() { return itemId; }
        public int getWeight() { return weight; }

        public static ExtraSupplies getRandomSupply() {
            int random = Utils.random(TOTAL_WEIGHT);
            int currentWeight = 0;
            for (ExtraSupplies supply : VALUES) {
                currentWeight += supply.getWeight();
                if (random < currentWeight) {
                    return supply;
                }
            }
            return BLIGHTED_ANGLERFISH; // Fallback
        }
    }

    /**
     * Enum representing resource rewards based on lap count
     */
    public enum ResourceReward {
        // Laps 1-15 (1x multiplier)
        BLIGHTED_ANGLERFISH_1_15(ItemId.BLIGHTED_ANGLERFISH, 3, 6, 8, true),
        BLIGHTED_MANTA_RAY_1_15(ItemId.BLIGHTED_MANTA_RAY, 3, 6, 8, true),
        BLIGHTED_KARAMBWAN_1_15(ItemId.BLIGHTED_KARAMBWAN, 3, 6, 8, true),
        BLIGHTED_SUPER_RESTORE_1_15(ItemId.BLIGHTED_SUPER_RESTORE4, 1, 2, 7, true),

        // Laps 16-30 (2x multiplier)
        BLIGHTED_ANGLERFISH_16_30(ItemId.BLIGHTED_ANGLERFISH, 6, 11, 8, true),
        BLIGHTED_MANTA_RAY_16_30(ItemId.BLIGHTED_MANTA_RAY, 6, 11, 8, true),
        BLIGHTED_KARAMBWAN_16_30(ItemId.BLIGHTED_KARAMBWAN, 6, 11, 8, true),
        BLIGHTED_SUPER_RESTORE_16_30(ItemId.BLIGHTED_SUPER_RESTORE4, 2, 4, 7, true),

        // Laps 31-60 (3x multiplier)
        BLIGHTED_ANGLERFISH_31_60(ItemId.BLIGHTED_ANGLERFISH, 10, 16, 8, true),
        BLIGHTED_MANTA_RAY_31_60(ItemId.BLIGHTED_MANTA_RAY, 10, 16, 8, true),
        BLIGHTED_KARAMBWAN_31_60(ItemId.BLIGHTED_KARAMBWAN, 10, 16, 8, true),
        BLIGHTED_SUPER_RESTORE_31_60(ItemId.BLIGHTED_SUPER_RESTORE4, 3, 6, 7, true),

        // Laps 61+ (4x multiplier)
        BLIGHTED_ANGLERFISH_61_PLUS(ItemId.BLIGHTED_ANGLERFISH, 14, 20, 8, true),
        BLIGHTED_MANTA_RAY_61_PLUS(ItemId.BLIGHTED_MANTA_RAY, 14, 20, 8, true),
        BLIGHTED_KARAMBWAN_61_PLUS(ItemId.BLIGHTED_KARAMBWAN, 14, 20, 8, true),
        BLIGHTED_SUPER_RESTORE_61_PLUS(ItemId.BLIGHTED_SUPER_RESTORE4, 4, 8, 7, true);

        private final int itemId;
        private final int minQuantity;
        private final int maxQuantity;
        private final int weight;
        private final boolean noted;

        private static final int TOTAL_WEIGHT = 31; // 8+8+8+7 = 31

        ResourceReward(int itemId, int minQuantity, int maxQuantity, int weight, boolean noted) {
            this.itemId = itemId;
            this.minQuantity = minQuantity;
            this.maxQuantity = maxQuantity;
            this.weight = weight;
            this.noted = noted;
        }

        public int getItemId() { return itemId; }
        public int getMinQuantity() { return minQuantity; }
        public int getMaxQuantity() { return maxQuantity; }
        public int getWeight() { return weight; }
        public boolean isNoted() { return noted; }

        public static ResourceReward[] getRewardsForLapCount(int lapCount) {
            if (lapCount <= 15) {
                return new ResourceReward[]{
                    BLIGHTED_ANGLERFISH_1_15, BLIGHTED_MANTA_RAY_1_15,
                    BLIGHTED_KARAMBWAN_1_15, BLIGHTED_SUPER_RESTORE_1_15
                };
            } else if (lapCount <= 30) {
                return new ResourceReward[]{
                    BLIGHTED_ANGLERFISH_16_30, BLIGHTED_MANTA_RAY_16_30,
                    BLIGHTED_KARAMBWAN_16_30, BLIGHTED_SUPER_RESTORE_16_30
                };
            } else if (lapCount <= 60) {
                return new ResourceReward[]{
                    BLIGHTED_ANGLERFISH_31_60, BLIGHTED_MANTA_RAY_31_60,
                    BLIGHTED_KARAMBWAN_31_60, BLIGHTED_SUPER_RESTORE_31_60
                };
            } else {
                return new ResourceReward[]{
                    BLIGHTED_ANGLERFISH_61_PLUS, BLIGHTED_MANTA_RAY_61_PLUS,
                    BLIGHTED_KARAMBWAN_61_PLUS, BLIGHTED_SUPER_RESTORE_61_PLUS
                };
            }
        }

        public static ResourceReward getRandomResource(int lapCount) {
            ResourceReward[] availableRewards = getRewardsForLapCount(lapCount);
            int random = Utils.random(TOTAL_WEIGHT);
            int currentWeight = 0;
            for (ResourceReward reward : availableRewards) {
                currentWeight += reward.getWeight();
                if (random < currentWeight) {
                    return reward;
                }
            }
            return availableRewards[0]; // Fallback
        }
    }

    private final Consumer<Player> completeConsumer = player -> {
        // Always give wilderness agility ticket (official OSRS reward)
        player.getInventory().addItem(new Item(28098, 1)); // Wilderness agility ticket

        // Get player's current lap count (you may need to implement lap tracking)
        int lapCount = getLapCount(player); // This method needs to be implemented

        // Give resource reward (noted)
        ResourceReward resourceReward = ResourceReward.getRandomResource(lapCount);
        int quantity = Utils.random(resourceReward.getMinQuantity(), resourceReward.getMaxQuantity());
        Item resourceItem = new Item(resourceReward.getItemId(), quantity);
        if (resourceReward.isNoted()) {
            resourceItem.setId(resourceItem.getDefinitions().getNotedId());
        }
        player.getInventory().addItem(resourceItem);

        // Give armour reward (noted) - simplified for now
        giveArmourReward(player, lapCount);

        // Give extra supply if inventory space available
        if (player.getInventory().getFreeSlots() > 0) {
            ExtraSupplies extraSupply = ExtraSupplies.getRandomSupply();
            player.getInventory().addItem(new Item(extraSupply.getItemId(), 1));
        }

        // Medium clue scroll chance (1/40 base, 1/20 with ring of wealth (i))
        if (Utils.random(40) == 0) { // Simplified to 1/40 for now
            if (player.getInventory().getFreeSlots() > 0) {
                player.getInventory().addItem(new Item(ItemId.CLUE_SCROLL_MEDIUM, 1));
            } else {
                // Drop to ground if inventory full
                player.getDroppedItems().add(new Item(ItemId.CLUE_SCROLL_MEDIUM, 1), player.getLocation(), player, 60, true, 200, true, false);
            }
        }

        // Custom server rewards
        player.getInventory().addItem(new Item(ItemId.BLOOD_MONEY, Utils.random(2, 6)));
        player.getInventory().addItem(new Item(995, 50000));
    };

    /**
     * Enum representing armour rewards based on lap count
     */
    public enum ArmourReward {
        // Laps 1-15
        ADAMANT_FULL_HELM_1_15(ItemId.ADAMANT_FULL_HELM, 1, true, 0),
        ADAMANT_PLATEBODY_1_15(ItemId.ADAMANT_PLATEBODY, 2, true, 0),
        ADAMANT_PLATELEGS_1_15(ItemId.ADAMANT_PLATELEGS, 1, true, 0),
        MITHRIL_CHAINBODY_1_15(ItemId.MITHRIL_CHAINBODY, 1, true, 0),
        MITHRIL_PLATELEGS_1_15(ItemId.MITHRIL_PLATELEGS, 1, true, 0),
        MITHRIL_PLATESKIRT_1_15(ItemId.MITHRIL_PLATESKIRT, 1, true, 0),
        RUNE_MED_HELM_1_15(ItemId.RUNE_MED_HELM, 2, true, 0),
        STEEL_PLATEBODY_1_15(ItemId.STEEL_PLATEBODY, 1, true, 0),
        COINS_1_15(995, 2, false, 50000), // Custom server reward - as rare as rune items
        BLOOD_MONEY_1_15(ItemId.BLOOD_MONEY, 2, false, Utils.random(2, 6)), // Custom server reward

        // Laps 16-30
        ADAMANT_FULL_HELM_16_30(ItemId.ADAMANT_FULL_HELM, 1, true),
        ADAMANT_PLATEBODY_16_30(ItemId.ADAMANT_PLATEBODY, 1, true),
        ADAMANT_PLATELEGS_16_30(ItemId.ADAMANT_PLATELEGS, 1, true),
        MITHRIL_CHAINBODY_16_30(ItemId.MITHRIL_CHAINBODY, 1, true),
        MITHRIL_PLATELEGS_16_30(ItemId.MITHRIL_PLATELEGS, 1, true),
        MITHRIL_PLATESKIRT_16_30(ItemId.MITHRIL_PLATESKIRT, 1, true),
        RUNE_CHAINBODY_16_30(ItemId.RUNE_CHAINBODY, 1, true),
        RUNE_KITESHIELD_16_30(ItemId.RUNE_KITESHIELD, 1, true),
        RUNE_MED_HELM_16_30(ItemId.RUNE_MED_HELM, 1, true),

        // Laps 31-60
        ADAMANT_FULL_HELM_31_60(ItemId.ADAMANT_FULL_HELM, 1, true),
        ADAMANT_PLATEBODY_31_60(ItemId.ADAMANT_PLATEBODY, 1, true),
        ADAMANT_PLATELEGS_31_60(ItemId.ADAMANT_PLATELEGS, 1, true),
        MITHRIL_PLATELEGS_31_60(ItemId.MITHRIL_PLATELEGS, 1, true),
        MITHRIL_PLATESKIRT_31_60(ItemId.MITHRIL_PLATESKIRT, 1, true),
        RUNE_CHAINBODY_31_60(ItemId.RUNE_CHAINBODY, 2, true),
        RUNE_KITESHIELD_31_60(ItemId.RUNE_KITESHIELD, 2, true),
        RUNE_MED_HELM_31_60(ItemId.RUNE_MED_HELM, 1, true),

        // Laps 61+
        ADAMANT_FULL_HELM_61_PLUS(ItemId.ADAMANT_FULL_HELM, 1, true),
        ADAMANT_PLATEBODY_61_PLUS(ItemId.ADAMANT_PLATEBODY, 2, true),
        ADAMANT_PLATELEGS_61_PLUS(ItemId.ADAMANT_PLATELEGS, 1, true),
        MITHRIL_PLATELEGS_61_PLUS(ItemId.MITHRIL_PLATELEGS, 1, true),
        MITHRIL_PLATESKIRT_61_PLUS(ItemId.MITHRIL_PLATESKIRT, 1, true),
        RUNE_CHAINBODY_61_PLUS(ItemId.RUNE_CHAINBODY, 6, true),
        RUNE_KITESHIELD_61_PLUS(ItemId.RUNE_KITESHIELD, 6, true),
        RUNE_MED_HELM_61_PLUS(ItemId.RUNE_MED_HELM, 2, true);

        private final int itemId;
        private final int weight;
        private final boolean noted;

        ArmourReward(int itemId, int weight, boolean noted) {
            this.itemId = itemId;
            this.weight = weight;
            this.noted = noted;
        }

        public int getItemId() { return itemId; }
        public int getWeight() { return weight; }
        public boolean isNoted() { return noted; }

        public static ArmourReward[] getArmourForLapCount(int lapCount) {
            if (lapCount <= 15) {
                return new ArmourReward[]{
                    ADAMANT_FULL_HELM_1_15, ADAMANT_PLATEBODY_1_15, ADAMANT_PLATELEGS_1_15,
                    MITHRIL_CHAINBODY_1_15, MITHRIL_PLATELEGS_1_15, MITHRIL_PLATESKIRT_1_15,
                    RUNE_MED_HELM_1_15, STEEL_PLATEBODY_1_15
                };
            } else if (lapCount <= 30) {
                return new ArmourReward[]{
                    ADAMANT_FULL_HELM_16_30, ADAMANT_PLATEBODY_16_30, ADAMANT_PLATELEGS_16_30,
                    MITHRIL_CHAINBODY_16_30, MITHRIL_PLATELEGS_16_30, MITHRIL_PLATESKIRT_16_30,
                    RUNE_CHAINBODY_16_30, RUNE_KITESHIELD_16_30, RUNE_MED_HELM_16_30
                };
            } else if (lapCount <= 60) {
                return new ArmourReward[]{
                    ADAMANT_FULL_HELM_31_60, ADAMANT_PLATEBODY_31_60, ADAMANT_PLATELEGS_31_60,
                    MITHRIL_PLATELEGS_31_60, MITHRIL_PLATESKIRT_31_60, RUNE_CHAINBODY_31_60,
                    RUNE_KITESHIELD_31_60, RUNE_MED_HELM_31_60
                };
            } else {
                return new ArmourReward[]{
                    ADAMANT_FULL_HELM_61_PLUS, ADAMANT_PLATEBODY_61_PLUS, ADAMANT_PLATELEGS_61_PLUS,
                    MITHRIL_PLATELEGS_61_PLUS, MITHRIL_PLATESKIRT_61_PLUS, RUNE_CHAINBODY_61_PLUS,
                    RUNE_KITESHIELD_61_PLUS, RUNE_MED_HELM_61_PLUS
                };
            }
        }

        public static ArmourReward getRandomArmour(int lapCount) {
            ArmourReward[] availableArmour = getArmourForLapCount(lapCount);
            int totalWeight = 0;
            for (ArmourReward armour : availableArmour) {
                totalWeight += armour.getWeight();
            }

            int random = Utils.random(totalWeight);
            int currentWeight = 0;
            for (ArmourReward armour : availableArmour) {
                currentWeight += armour.getWeight();
                if (random < currentWeight) {
                    return armour;
                }
            }
            return availableArmour[0]; // Fallback
        }
    }

    /**
     * Helper method to give armour reward
     */
    private void giveArmourReward(Player player, int lapCount) {
        ArmourReward armourReward = ArmourReward.getRandomArmour(lapCount);
        Item armourItem = new Item(armourReward.getItemId(), 1);
        if (armourReward.isNoted()) {
            armourItem.setId(armourItem.getDefinitions().getNotedId());
        }
        player.getInventory().addItem(armourItem);
    }

    /**
     * Helper method to get player's lap count
     * This should be implemented to track player's consecutive laps
     */
    private int getLapCount(Player player) {
        // For now, return a default value
        // In a full implementation, you would track this in player attributes
        return player.getNumericAttribute("wilderness_agility_laps").intValue();
    }

    @Override
    public double getAdditionalCompletionXP() {
        return 499;
    }

    @Override
    public Consumer<Player> onComplete() {
        return completeConsumer;
    }

}
